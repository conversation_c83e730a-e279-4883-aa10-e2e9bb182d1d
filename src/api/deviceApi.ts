import { type Device } from '../types/device';

const API_BASE_URL = 'http://localhost:5000/api';

export class DeviceApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'DeviceApiError';
  }
}

export const fetchDeviceInfo = async (serial: string): Promise<DeviceApiResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/pifconfig/${serial}`);
    
    if (!response.ok) {
      throw new DeviceApiError(
        `Failed to fetch device info: ${response.statusText}`,
        response.status
      );
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    if (error instanceof DeviceApiError) {
      throw error;
    }
    throw new DeviceApiError(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const fetchAllDevicesInfo = async (serials: string[]): Promise<DeviceApiResponse[]> => {
  const promises = serials.map(serial => 
    fetchDeviceInfo(serial).catch(error => {
      console.warn(`Failed to fetch info for device ${serial}:`, error);
      return null;
    })
  );
  
  const results = await Promise.all(promises);
  return results.filter((result): result is DeviceApiResponse => result !== null);
};

export const transformApiResponseToDevice = async (
  apiResponse: DeviceApiResponse,
  existingDevice?: Partial<Device>
): Promise<Device> => {
  const { serial, content } = apiResponse;
  const { build, props } = content;

  // Try to get additional device info from other API endpoints
  let batteryLevel = existingDevice?.batteryLevel;
  let isCharging = existingDevice?.isCharging;
  let ipAddress = existingDevice?.ipAddress || 'Unknown';
  let uptime = existingDevice?.uptime;

  try {
    const batteryInfo = await getDeviceBatteryInfo(serial);
    batteryLevel = batteryInfo.level;
    isCharging = batteryInfo.isCharging;
  } catch (err) {
    console.warn(`Failed to get battery info for ${serial}:`, err);
  }

  try {
    ipAddress = await getDeviceIpAddress(serial);
  } catch (err) {
    console.warn(`Failed to get IP address for ${serial}:`, err);
  }

  try {
    uptime = await getDeviceUptime(serial);
  } catch (err) {
    console.warn(`Failed to get uptime for ${serial}:`, err);
  }

  return {
    id: existingDevice?.id || serial,
    name: existingDevice?.name || `Device-${serial.slice(-4)}`,
    alias: existingDevice?.alias,
    ipAddress,
    serialNumber: serial,
    adbStatus: existingDevice?.adbStatus || 'connected',
    sshStatus: existingDevice?.sshStatus || 'reachable',
    buildFingerprint: build.FINGERPRINT,
    productModel: build.MODEL,
    buildVersion: build.RELEASE,
    batteryLevel,
    isCharging,
    uptime,
    lastSeen: new Date(),
    isOnline: true,
    // Enhanced API data
    manufacturer: build.MANUFACTURER,
    brand: build.BRAND,
    deviceCodename: build.DEVICE,
    buildId: build.ID,
    buildIncremental: build.INCREMENTAL,
    buildType: build.TYPE,
    buildTags: build.TAGS,
    securityPatch: build.SECURITY_PATCH,
    apiLevel: props.api_level,
    deviceInitialSdk: build.DEVICE_INITIAL_SDK_INT,
  };
};

// Get list of connected ADB devices - YOU NEED TO IMPLEMENT THIS API
export const getConnectedDevices = async (): Promise<string[]> => {
  // TODO: Implement API endpoint to get connected device serials
  // This should call something like: GET /api/devices/connected
  // Which runs `adb devices` command and returns array of serial numbers

  try {
    const response = await fetch(`${API_BASE_URL}/devices/connected`);
    if (!response.ok) {
      throw new DeviceApiError('Failed to get connected devices', response.status);
    }
    const data = await response.json();
    return data.serials || []; // Expecting: { serials: ["serial1", "serial2", ...] }
  } catch (error) {
    console.error('Failed to get connected devices:', error);
    throw new DeviceApiError('Failed to get connected devices');
  }
};

export const refreshDeviceData = async (devices: Device[]): Promise<Device[]> => {
  const serials = devices.map(d => d.serialNumber);
  const apiResponses = await fetchAllDevicesInfo(serials);

  const updatedDevices = await Promise.all(
    devices.map(async device => {
      const apiResponse = apiResponses.find(r => r.serial === device.serialNumber);
      if (apiResponse) {
        return await transformApiResponseToDevice(apiResponse, device);
      }
      // If API call failed, mark as offline but keep existing data
      return {
        ...device,
        isOnline: false,
        adbStatus: 'disconnected' as const,
        lastSeen: new Date(),
      };
    })
  );

  return updatedDevices;
};

// MISSING API ENDPOINTS - YOU NEED TO IMPLEMENT THESE:

// 1. Get device battery info - YOU NEED TO IMPLEMENT THIS
export const getDeviceBatteryInfo = async (serial: string): Promise<{level: number, isCharging: boolean}> => {
  // TODO: Implement API endpoint: GET /api/devices/{serial}/battery
  // Should run: adb -s {serial} shell dumpsys battery
  try {
    const response = await fetch(`${API_BASE_URL}/devices/${serial}/battery`);
    if (!response.ok) throw new DeviceApiError('Failed to get battery info', response.status);
    const data = await response.json();
    return { level: data.level, isCharging: data.isCharging };
  } catch (error) {
    throw new DeviceApiError(`Failed to get battery info: ${error}`);
  }
};

// 2. Get device IP address - YOU NEED TO IMPLEMENT THIS
export const getDeviceIpAddress = async (serial: string): Promise<string> => {
  // TODO: Implement API endpoint: GET /api/devices/{serial}/ip
  // Should run: adb -s {serial} shell ip route | grep wlan
  try {
    const response = await fetch(`${API_BASE_URL}/devices/${serial}/ip`);
    if (!response.ok) throw new DeviceApiError('Failed to get IP address', response.status);
    const data = await response.json();
    return data.ipAddress;
  } catch (error) {
    throw new DeviceApiError(`Failed to get IP address: ${error}`);
  }
};

// 3. Get device uptime - YOU NEED TO IMPLEMENT THIS
export const getDeviceUptime = async (serial: string): Promise<string> => {
  // TODO: Implement API endpoint: GET /api/devices/{serial}/uptime
  // Should run: adb -s {serial} shell uptime
  try {
    const response = await fetch(`${API_BASE_URL}/devices/${serial}/uptime`);
    if (!response.ok) throw new DeviceApiError('Failed to get uptime', response.status);
    const data = await response.json();
    return data.uptime;
  } catch (error) {
    throw new DeviceApiError(`Failed to get uptime: ${error}`);
  }
};

// 4. Reboot device - YOU NEED TO IMPLEMENT THIS
export const rebootDevice = async (serial: string): Promise<void> => {
  // TODO: Implement API endpoint: POST /api/devices/{serial}/reboot
  // Should run: adb -s {serial} reboot
  try {
    const response = await fetch(`${API_BASE_URL}/devices/${serial}/reboot`, {
      method: 'POST'
    });
    if (!response.ok) throw new DeviceApiError('Failed to reboot device', response.status);
  } catch (error) {
    throw new DeviceApiError(`Failed to reboot device: ${error}`);
  }
};

// 5. Execute ADB shell command - YOU NEED TO IMPLEMENT THIS
export const executeAdbCommand = async (serial: string, command: string): Promise<string> => {
  // TODO: Implement API endpoint: POST /api/devices/{serial}/shell
  // Should run: adb -s {serial} shell {command}
  try {
    const response = await fetch(`${API_BASE_URL}/devices/${serial}/shell`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ command })
    });
    if (!response.ok) throw new DeviceApiError('Failed to execute command', response.status);
    const data = await response.json();
    return data.output;
  } catch (error) {
    throw new DeviceApiError(`Failed to execute command: ${error}`);
  }
};

// 6. Check SSH connectivity - YOU NEED TO IMPLEMENT THIS
export const checkSshConnectivity = async (serial: string, ipAddress: string): Promise<boolean> => {
  // TODO: Implement API endpoint: GET /api/devices/{serial}/ssh-status
  // Should test SSH connection to the device
  try {
    const response = await fetch(`${API_BASE_URL}/devices/${serial}/ssh-status`);
    if (!response.ok) throw new DeviceApiError('Failed to check SSH', response.status);
    const data = await response.json();
    return data.isReachable;
  } catch (error) {
    throw new DeviceApiError(`Failed to check SSH: ${error}`);
  }
};

// 7. Restart SSH service - YOU NEED TO IMPLEMENT THIS
export const restartSshService = async (serial: string): Promise<void> => {
  // TODO: Implement API endpoint: POST /api/devices/{serial}/restart-ssh
  // Should restart SSH service on the device
  try {
    const response = await fetch(`${API_BASE_URL}/devices/${serial}/restart-ssh`, {
      method: 'POST'
    });
    if (!response.ok) throw new DeviceApiError('Failed to restart SSH', response.status);
  } catch (error) {
    throw new DeviceApiError(`Failed to restart SSH: ${error}`);
  }
};

// 8. Ping device - YOU NEED TO IMPLEMENT THIS
export const pingDevice = async (serial: string): Promise<boolean> => {
  // TODO: Implement API endpoint: GET /api/devices/{serial}/ping
  // Should check if device is responsive via ADB
  try {
    const response = await fetch(`${API_BASE_URL}/devices/${serial}/ping`);
    if (!response.ok) throw new DeviceApiError('Failed to ping device', response.status);
    const data = await response.json();
    return data.isOnline;
  } catch (error) {
    throw new DeviceApiError(`Failed to ping device: ${error}`);
  }
};
