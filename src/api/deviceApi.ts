import { type Device, type DeviceApiResponse } from '../types/device';

const API_BASE_URL = 'http://localhost:5000/api';

export class DeviceApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'DeviceApiError';
  }
}

export const fetchDeviceInfo = async (serial: string): Promise<DeviceApiResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/pifconfig/${serial}`);
    
    if (!response.ok) {
      throw new DeviceApiError(
        `Failed to fetch device info: ${response.statusText}`,
        response.status
      );
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    if (error instanceof DeviceApiError) {
      throw error;
    }
    throw new DeviceApiError(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const fetchAllDevicesInfo = async (serials: string[]): Promise<DeviceApiResponse[]> => {
  const promises = serials.map(serial => 
    fetchDeviceInfo(serial).catch(error => {
      console.warn(`Failed to fetch info for device ${serial}:`, error);
      return null;
    })
  );
  
  const results = await Promise.all(promises);
  return results.filter((result): result is DeviceApiResponse => result !== null);
};

export const transformApiResponseToDevice = (
  apiResponse: DeviceApiResponse,
  existingDevice?: Partial<Device>
): Device => {
  const { serial, content } = apiResponse;
  const { build, props } = content;
  
  return {
    id: existingDevice?.id || serial,
    name: existingDevice?.name || `Device-${serial.slice(-4)}`,
    alias: existingDevice?.alias,
    ipAddress: existingDevice?.ipAddress || 'Unknown',
    serialNumber: serial,
    adbStatus: existingDevice?.adbStatus || 'connected',
    sshStatus: existingDevice?.sshStatus || 'reachable',
    buildFingerprint: build.FINGERPRINT,
    productModel: build.MODEL,
    buildVersion: build.RELEASE,
    batteryLevel: existingDevice?.batteryLevel,
    isCharging: existingDevice?.isCharging,
    uptime: existingDevice?.uptime,
    lastSeen: new Date(),
    isOnline: true,
    // Enhanced API data
    manufacturer: build.MANUFACTURER,
    brand: build.BRAND,
    deviceCodename: build.DEVICE,
    buildId: build.ID,
    buildIncremental: build.INCREMENTAL,
    buildType: build.TYPE,
    buildTags: build.TAGS,
    securityPatch: build.SECURITY_PATCH,
    apiLevel: props.api_level,
    deviceInitialSdk: build.DEVICE_INITIAL_SDK_INT,
  };
};

// Get list of connected ADB devices (mock for now - you can implement real ADB command)
export const getConnectedDevices = async (): Promise<string[]> => {
  // This would typically run `adb devices` command
  // For now, return mock serials that match your API
  return [
    '8CBX1LZV6',
    'ABC123DEF456',
    'GHI789JKL012',
    'MNO345PQR678',
    'STU901VWX234',
    'YZA567BCD890'
  ];
};

export const refreshDeviceData = async (devices: Device[]): Promise<Device[]> => {
  const serials = devices.map(d => d.serialNumber);
  const apiResponses = await fetchAllDevicesInfo(serials);
  
  return devices.map(device => {
    const apiResponse = apiResponses.find(r => r.serial === device.serialNumber);
    if (apiResponse) {
      return transformApiResponseToDevice(apiResponse, device);
    }
    // If API call failed, mark as offline but keep existing data
    return {
      ...device,
      isOnline: false,
      adbStatus: 'disconnected' as const,
      lastSeen: new Date(),
    };
  });
};
