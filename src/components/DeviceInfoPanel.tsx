import React, { useState } from 'react';
import {
  ChevronDown,
  ChevronRight,
  Shield,
  Cpu,
  Info,
  Calendar,
  Hash,
  Tag,
  Building2,
  Smartphone,
  Battery
} from 'lucide-react';
import { Badge } from './ui/Badge';
import { BatteryInfoPanel } from './BatteryInfoPanel';
import { type Device } from '../types/device';

interface DeviceInfoPanelProps {
  device: Device;
}

export const DeviceInfoPanel: React.FC<DeviceInfoPanelProps> = ({ device }) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const InfoSection: React.FC<{
    title: string;
    icon: React.ReactNode;
    children: React.ReactNode;
    sectionKey: string;
    defaultExpanded?: boolean;
  }> = ({ title, icon, children, sectionKey, defaultExpanded = false }) => {
    const isExpanded = expandedSections.has(sectionKey) || defaultExpanded;
    
    return (
      <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <button
          onClick={() => toggleSection(sectionKey)}
          className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center justify-between text-left"
        >
          <div className="flex items-center space-x-2">
            {icon}
            <span className="font-medium text-gray-900 dark:text-gray-100">{title}</span>
          </div>
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 text-gray-500" />
          ) : (
            <ChevronRight className="w-4 h-4 text-gray-500" />
          )}
        </button>
        {isExpanded && (
          <div className="px-4 py-3 bg-white dark:bg-gray-800">
            {children}
          </div>
        )}
      </div>
    );
  };

  const InfoRow: React.FC<{ label: string; value: string | undefined; mono?: boolean }> = ({ 
    label, 
    value, 
    mono = false 
  }) => (
    <div className="flex justify-between items-start py-1">
      <span className="text-sm text-gray-500 dark:text-gray-400 min-w-0 flex-shrink-0 mr-3">
        {label}:
      </span>
      <span className={`text-sm text-gray-900 dark:text-gray-100 text-right break-all ${
        mono ? 'font-mono' : ''
      }`}>
        {value || 'N/A'}
      </span>
    </div>
  );

  const getSecurityPatchStatus = (patch?: string) => {
    if (!patch) return { variant: 'secondary' as const, label: 'Unknown' };
    
    const patchDate = new Date(patch);
    const now = new Date();
    const monthsOld = (now.getFullYear() - patchDate.getFullYear()) * 12 + 
                     (now.getMonth() - patchDate.getMonth());
    
    if (monthsOld <= 2) return { variant: 'success' as const, label: 'Current' };
    if (monthsOld <= 6) return { variant: 'warning' as const, label: 'Outdated' };
    return { variant: 'danger' as const, label: 'Very Outdated' };
  };

  const securityStatus = getSecurityPatchStatus(device.securityPatch);

  return (
    <div className="space-y-3">
      {/* Basic Device Info */}
      <InfoSection
        title="Device Information"
        icon={<Smartphone className="w-4 h-4 text-blue-500" />}
        sectionKey="basic"
        defaultExpanded={true}
      >
        <div className="space-y-1">
          <InfoRow label="Manufacturer" value={device.manufacturer} />
          <InfoRow label="Brand" value={device.brand} />
          <InfoRow label="Model" value={device.productModel} />
          <InfoRow label="Codename" value={device.deviceCodename} />
          <InfoRow label="Serial" value={device.serialNumber} mono />
        </div>
      </InfoSection>

      {/* Build Information */}
      <InfoSection
        title="Build Information"
        icon={<Building2 className="w-4 h-4 text-green-500" />}
        sectionKey="build"
      >
        <div className="space-y-1">
          <InfoRow label="Android Version" value={device.buildVersion} />
          <InfoRow label="API Level" value={device.apiLevel} />
          <InfoRow label="Build ID" value={device.buildId} mono />
          <InfoRow label="Build Incremental" value={device.buildIncremental} mono />
          <InfoRow label="Build Type" value={device.buildType} />
          <InfoRow label="Build Tags" value={device.buildTags} />
          <InfoRow label="Initial SDK" value={device.deviceInitialSdk} />
        </div>
      </InfoSection>

      {/* Security Information */}
      <InfoSection
        title="Security Information"
        icon={<Shield className="w-4 h-4 text-red-500" />}
        sectionKey="security"
      >
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500 dark:text-gray-400">Security Patch:</span>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-mono text-gray-900 dark:text-gray-100">
                {device.securityPatch || 'N/A'}
              </span>
              <Badge variant={securityStatus.variant} size="sm">
                {securityStatus.label}
              </Badge>
            </div>
          </div>
          {device.securityPatch && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Last updated: {new Date(device.securityPatch).toLocaleDateString()}
            </div>
          )}
        </div>
      </InfoSection>

      {/* Build Fingerprint */}
      <InfoSection
        title="Build Fingerprint"
        icon={<Hash className="w-4 h-4 text-purple-500" />}
        sectionKey="fingerprint"
      >
        <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
          <code className="text-xs text-gray-800 dark:text-gray-200 break-all leading-relaxed">
            {device.buildFingerprint}
          </code>
        </div>
      </InfoSection>
    </div>
  );
};
