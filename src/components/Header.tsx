import React from 'react';
import { Moon, Sun, Smartphone, Activity } from 'lucide-react';
import { Button } from './ui/Button';
import { Badge } from './ui/Badge';
import { Toggle } from './ui/Toggle';
import { useTheme } from '../context/ThemeContext';
import type {DeviceStats} from "../types/device.ts";

interface HeaderProps {
  stats: DeviceStats;
  autoRefresh: boolean;
  onToggleAutoRefresh: (enabled: boolean) => void;
}

export const Header: React.FC<HeaderProps> = ({
  stats,
  autoRefresh,
  onToggleAutoRefresh,
  useRealApi = false,
  onToggleApiMode,
}) => {
  const { theme, toggleTheme } = useTheme();

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Title */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Smartphone className="w-8 h-8 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                
              </h1>
            </div>
          </div>

          {/* Stats */}
          <div className="hidden md:flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-gray-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Total: {stats.totalDevices}
              </span>
            </div>
            
            <Badge variant="success" size="sm">
              Online: {stats.onlineDevices}
            </Badge>
            
            {stats.offlineDevices > 0 && (
              <Badge variant="secondary" size="sm">
                Offline: {stats.offlineDevices}
              </Badge>
            )}
            
            <Badge variant="success" size="sm">
              Healthy: {stats.healthyDevices}
            </Badge>
            
            {stats.warningDevices > 0 && (
              <Badge variant="warning" size="sm">
                Warning: {stats.warningDevices}
              </Badge>
            )}
            
            {stats.criticalDevices > 0 && (
              <Badge variant="danger" size="sm">
                Critical: {stats.criticalDevices}
              </Badge>
            )}
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-4">
            {/* API Status */}
            <div className="hidden sm:flex items-center space-x-2">
              <Badge
                variant={useRealApi ? 'success' : 'warning'}
                size="sm"
              >
                {useRealApi ? '🔗 Live API' : '🔧 Mock Data'}
              </Badge>
              {onToggleApiMode && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggleApiMode}
                  title="Toggle between real API and mock data"
                >
                  Switch
                </Button>
              )}
            </div>

            {/* Auto Refresh Toggle */}
            <div className="flex items-center space-x-2">
              <Toggle
                checked={autoRefresh}
                onChange={onToggleAutoRefresh}
                label="Auto Refresh"
              />
            </div>

            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleTheme}
              aria-label="Toggle theme"
            >
              {theme === 'light' ? (
                <Moon className="w-5 h-5" />
              ) : (
                <Sun className="w-5 h-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Stats */}
        <div className="md:hidden pb-4">
          <div className="flex flex-wrap gap-2">
            <Badge variant="info" size="sm">
              Total: {stats.totalDevices}
            </Badge>
            <Badge variant="success" size="sm">
              Online: {stats.onlineDevices}
            </Badge>
            {stats.offlineDevices > 0 && (
              <Badge variant="secondary" size="sm">
                Offline: {stats.offlineDevices}
              </Badge>
            )}
            <Badge variant="success" size="sm">
              Healthy: {stats.healthyDevices}
            </Badge>
            {stats.warningDevices > 0 && (
              <Badge variant="warning" size="sm">
                Warning: {stats.warningDevices}
              </Badge>
            )}
            {stats.criticalDevices > 0 && (
              <Badge variant="danger" size="sm">
                Critical: {stats.criticalDevices}
              </Badge>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};
