import React from 'react';
import { Wifi, WifiOff, AlertTriangle } from 'lucide-react';
import { Badge } from './ui/Badge';

interface ApiStatusIndicatorProps {
  isConnected: boolean;
  lastUpdate?: Date;
  error?: string | null;
}

export const ApiStatusIndicator: React.FC<ApiStatusIndicatorProps> = ({
  isConnected,
  lastUpdate,
  error
}) => {
  const getStatusIcon = () => {
    if (error) return <AlertTriangle className="w-4 h-4" />;
    return isConnected ? <Wifi className="w-4 h-4" /> : <WifiOff className="w-4 h-4" />;
  };

  const getStatusVariant = () => {
    if (error) return 'danger' as const;
    return isConnected ? 'success' as const : 'secondary' as const;
  };

  const getStatusText = () => {
    if (error) return 'API Error';
    return isConnected ? 'Live API' : 'Mock Data';
  };

  return (
    <div className="flex items-center space-x-2">
      <Badge variant={getStatusVariant()} size="sm">
        <div className="flex items-center space-x-1">
          {getStatusIcon()}
          <span>{getStatusText()}</span>
        </div>
      </Badge>
      {lastUpdate && isConnected && (
        <span className="text-xs text-gray-500 dark:text-gray-400">
          Updated: {lastUpdate.toLocaleTimeString()}
        </span>
      )}
      {error && (
        <span className="text-xs text-red-500 dark:text-red-400" title={error}>
          Connection failed
        </span>
      )}
    </div>
  );
};
