import React from 'react';
import { Battery, Zap, Thermometer, Gauge } from 'lucide-react';
import { Badge } from './ui/Badge';
import { BatteryInfo } from '../types/device';

interface BatteryInfoPanelProps {
  batteryInfo: BatteryInfo;
}

export const BatteryInfoPanel: React.FC<BatteryInfoPanelProps> = ({ batteryInfo }) => {
  const getBatteryStatusText = (status: number) => {
    const statusMap = {
      1: 'Unknown',
      2: 'Charging',
      3: 'Discharging',
      4: 'Not charging',
      5: 'Full'
    };
    return statusMap[status as keyof typeof statusMap] || 'Unknown';
  };

  const getBatteryHealthText = (health: number) => {
    const healthMap = {
      1: 'Unknown',
      2: 'Good',
      3: 'Overheat',
      4: 'Dead',
      5: 'Over voltage',
      6: 'Unspecified failure',
      7: 'Cold'
    };
    return healthMap[health as keyof typeof healthMap] || 'Unknown';
  };

  const getBatteryStatusVariant = (status: number) => {
    if (status === 2) return 'success'; // Charging
    if (status === 5) return 'success'; // Full
    if (status === 3) return 'warning'; // Discharging
    return 'secondary';
  };

  const getBatteryHealthVariant = (health: number) => {
    if (health === 2) return 'success'; // Good
    if (health === 3 || health === 7) return 'warning'; // Overheat/Cold
    if (health === 4 || health === 5 || health === 6) return 'danger'; // Dead/Over voltage/Failure
    return 'secondary';
  };

  const formatTemperature = (temp: number) => {
    const celsius = temp / 10;
    const fahrenheit = (celsius * 9/5) + 32;
    return `${celsius.toFixed(1)}°C (${fahrenheit.toFixed(1)}°F)`;
  };

  const formatVoltage = (voltage: number) => {
    return `${(voltage / 1000).toFixed(2)}V`;
  };

  const formatCurrent = (current: number) => {
    return `${(current / 1000).toFixed(1)}mA`;
  };

  const getPowerSources = () => {
    const sources = [];
    if (batteryInfo.acPowered) sources.push('AC');
    if (batteryInfo.usbPowered) sources.push('USB');
    if (batteryInfo.wirelessPowered) sources.push('Wireless');
    return sources.length > 0 ? sources.join(', ') : 'Battery';
  };

  return (
    <div className="space-y-4">
      {/* Battery Level and Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Battery className="w-5 h-5 text-green-500" />
          <span className="font-medium text-gray-900 dark:text-gray-100">
            {batteryInfo.level}% ({batteryInfo.level}/{batteryInfo.scale})
          </span>
        </div>
        <Badge variant={getBatteryStatusVariant(batteryInfo.status)} size="sm">
          {getBatteryStatusText(batteryInfo.status)}
        </Badge>
      </div>

      {/* Power Source */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Zap className="w-4 h-4 text-blue-500" />
          <span className="text-sm text-gray-600 dark:text-gray-300">Power Source</span>
        </div>
        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {getPowerSources()}
        </span>
      </div>

      {/* Battery Health */}
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600 dark:text-gray-300">Health</span>
        <Badge variant={getBatteryHealthVariant(batteryInfo.health)} size="sm">
          {getBatteryHealthText(batteryInfo.health)}
        </Badge>
      </div>

      {/* Temperature */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Thermometer className="w-4 h-4 text-red-500" />
          <span className="text-sm text-gray-600 dark:text-gray-300">Temperature</span>
        </div>
        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {formatTemperature(batteryInfo.temperature)}
        </span>
      </div>

      {/* Voltage */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Gauge className="w-4 h-4 text-yellow-500" />
          <span className="text-sm text-gray-600 dark:text-gray-300">Voltage</span>
        </div>
        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {formatVoltage(batteryInfo.voltage)}
        </span>
      </div>

      {/* Technology */}
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600 dark:text-gray-300">Technology</span>
        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {batteryInfo.technology}
        </span>
      </div>

      {/* Charging Specs */}
      {(batteryInfo.maxChargingCurrent > 0 || batteryInfo.maxChargingVoltage > 0) && (
        <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">Charging Specifications</div>
          
          {batteryInfo.maxChargingCurrent > 0 && (
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600 dark:text-gray-300">Max Current</span>
              <span className="text-gray-900 dark:text-gray-100">
                {formatCurrent(batteryInfo.maxChargingCurrent)}
              </span>
            </div>
          )}
          
          {batteryInfo.maxChargingVoltage > 0 && (
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600 dark:text-gray-300">Max Voltage</span>
              <span className="text-gray-900 dark:text-gray-100">
                {formatVoltage(batteryInfo.maxChargingVoltage)}
              </span>
            </div>
          )}
          
          {batteryInfo.chargeCounter > 0 && (
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600 dark:text-gray-300">Charge Counter</span>
              <span className="text-gray-900 dark:text-gray-100">
                {(batteryInfo.chargeCounter / 1000).toFixed(1)}mAh
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
