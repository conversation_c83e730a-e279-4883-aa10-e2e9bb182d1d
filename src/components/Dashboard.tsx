import React, { useState, useEffect } from 'react';
import { AlertCircle, X } from 'lucide-react';
import { Header } from './Header';
import { DeviceGrid } from './DeviceGrid';
import { BulkActions } from './BulkActions';
import { useDevices } from '../hooks/useDevices';

export const Dashboard: React.FC = () => {
  const { devices, stats, loading, error, actions } = useDevices();
  const [selectedDevices, setSelectedDevices] = useState<string[]>([]);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      actions.refreshDevices();
    }, 10000); // Refresh every 10 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, actions]);

  const handleToggleAutoRefresh = (enabled: boolean) => {
    setAutoRefresh(enabled);
  };

  const dismissError = () => {
    // In a real app, you'd have an error dismissal action
    console.log('Error dismissed');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header
        stats={stats}
        autoRefresh={autoRefresh}
        onToggleAutoRefresh={handleToggleAutoRefresh}
      />

      {/* Error Banner */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-400 mr-2" />
              <p className="text-red-700 dark:text-red-300">{error}</p>
            </div>
            <button
              onClick={dismissError}
              className="text-red-400 hover:text-red-600 dark:hover:text-red-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
      )}

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Bulk Actions */}
        <div className="mb-8">
          <BulkActions
            devices={devices}
            selectedDevices={selectedDevices}
            onRebootAll={actions.rebootAll}
            onPingAll={actions.pingAll}
            onRefresh={actions.refreshDevices}
          />
        </div>

        {/* Loading Overlay */}
        {loading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-900 dark:text-gray-100">Processing...</span>
            </div>
          </div>
        )}

        {/* Device Grid */}
        <DeviceGrid
          devices={devices}
          onReboot={actions.rebootDevice}
          onRestartSSH={actions.restartSSH}
          onCustomCommand={actions.executeCustomCommand}
          onPing={actions.pingDevice}
          onRefreshDevice={actions.fetchIndividualDeviceInfo}
        />

        {/* Footer */}
        <footer className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            <p>Phone Farm Dashboard - Real-time device monitoring and management</p>
            <p className="mt-1">
              Last updated: {new Date().toLocaleTimeString()} 
              {autoRefresh && ' • Auto-refresh enabled'}
            </p>
          </div>
        </footer>
      </main>
    </div>
  );
};
