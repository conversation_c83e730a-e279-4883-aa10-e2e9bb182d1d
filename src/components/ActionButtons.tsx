import React, { useState } from 'react';
import { Co<PERSON>, Power, Terminal, Wifi, MoreVertical, RefreshCw } from 'lucide-react';
import { Button } from './ui/Button';
import { copyToClipboard, showCopyNotification } from '../utils/clipboard';
import type {Device} from "../types/device.ts";

interface ActionButtonsProps {
  device: Device;
  onReboot: (deviceId: string) => void;
  onRestartSSH: (deviceId: string) => void;
  onCustomCommand: (deviceId: string, command: string) => void;
  onPing: (deviceId: string) => void;
  onRefreshDevice?: (deviceId: string) => void;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  device,
  onReboot,
  onRestartSSH,
  onCustomCommand,
  onPing,
  onRefreshDevice,
}) => {
  const [showCustomCommand, setShowCustomCommand] = useState(false);
  const [customCommand, setCustomCommand] = useState('');
  const [loading, setLoading] = useState<string | null>(null);

  const handleCopy = async (text: string, label: string) => {
    const success = await copyToClipboard(text);
    showCopyNotification(`${label}: ${text}`, success);
  };

  const handleAction = async (action: string, callback: () => void) => {
    setLoading(action);
    try {
      await callback();
    } finally {
      setLoading(null);
    }
  };

  const handleCustomCommand = () => {
    if (customCommand.trim()) {
      onCustomCommand(device.id, customCommand);
      setCustomCommand('');
      setShowCustomCommand(false);
    }
  };

  return (
    <div className="space-y-3">
      {/* Quick Copy Actions */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleCopy(device.ipAddress, 'IP Address')}
        >
          <Copy className="w-3 h-3 mr-1" />
          IP
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleCopy(device.serialNumber, 'Serial')}
        >
          <Copy className="w-3 h-3 mr-1" />
          Serial
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleCopy(device.buildFingerprint, 'Fingerprint')}
        >
          <Copy className="w-3 h-3 mr-1" />
          Fingerprint
        </Button>
      </div>

      {/* Device Actions */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant="secondary"
          size="sm"
          loading={loading === 'ping'}
          onClick={() => handleAction('ping', () => onPing(device.id))}
        >
          <Wifi className="w-3 h-3 mr-1" />
          Ping
        </Button>
        
        <Button
          variant="danger"
          size="sm"
          loading={loading === 'reboot'}
          onClick={() => handleAction('reboot', () => onReboot(device.id))}
          disabled={device.adbStatus === 'disconnected'}
        >
          <Power className="w-3 h-3 mr-1" />
          Reboot
        </Button>
        
        <Button
          variant="secondary"
          size="sm"
          loading={loading === 'ssh'}
          onClick={() => handleAction('ssh', () => onRestartSSH(device.id))}
          disabled={device.sshStatus === 'unreachable'}
        >
          <Terminal className="w-3 h-3 mr-1" />
          Restart SSH
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowCustomCommand(!showCustomCommand)}
        >
          <MoreVertical className="w-3 h-3 mr-1" />
          Custom
        </Button>
      </div>

      {/* Custom Command Input */}
      {showCustomCommand && (
        <div className="space-y-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <input
            type="text"
            value={customCommand}
            onChange={(e) => setCustomCommand(e.target.value)}
            placeholder="Enter ADB shell command..."
            className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            onKeyPress={(e) => e.key === 'Enter' && handleCustomCommand()}
          />
          <div className="flex gap-2">
            <Button
              variant="primary"
              size="sm"
              onClick={handleCustomCommand}
              disabled={!customCommand.trim() || device.adbStatus === 'disconnected'}
            >
              Execute
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setShowCustomCommand(false);
                setCustomCommand('');
              }}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
