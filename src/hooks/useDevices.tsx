import { useState, useEffect, useCallback } from 'react';
import {
  fetchAllDevices,
  refreshDeviceData,
  fetchDeviceInfo,
  rebootDevice as apiRebootDevice,
  executeAdbCommand,
  restartSshService,
  pingDevice as apiPingDevice
} from '../api/deviceApi';
import type {Device, DeviceStats} from "../types/device.ts";

export const useDevices = () => {
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Calculate device statistics
  const getStats = useCallback((): DeviceStats => {
    const totalDevices = devices.length;
    const onlineDevices = devices.filter(d => d.isOnline).length;
    const offlineDevices = totalDevices - onlineDevices;

    const getDeviceHealth = (device: Device) => {
      if (!device.isOnline) return 'offline';
      
      const issues = [
        device.adbStatus === 'disconnected',
        device.sshStatus === 'unreachable',
        (device.batteryLevel ?? 100) < 20,
      ].filter(Boolean).length;

      if (issues === 0) return 'healthy';
      if (issues === 1) return 'warning';
      return 'critical';
    };

    const healthyDevices = devices.filter(d => getDeviceHealth(d) === 'healthy').length;
    const warningDevices = devices.filter(d => getDeviceHealth(d) === 'warning').length;
    const criticalDevices = devices.filter(d => getDeviceHealth(d) === 'critical').length;

    return {
      totalDevices,
      onlineDevices,
      offlineDevices,
      healthyDevices,
      warningDevices,
      criticalDevices,
    };
  }, [devices]);

  // Real device actions using API
  const rebootDevice = useCallback(async (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    if (!device) return;

    setLoading(true);
    try {
      await apiRebootDevice(device.serialNumber);

      setDevices(prev => prev.map(d =>
        d.id === deviceId
          ? { ...d, lastSeen: new Date(), uptime: '0d 0h 0m' }
          : d
      ));

      console.log(`Rebooted device ${device.name}`);
    } catch (err) {
      setError(`Failed to reboot device ${device.name}: ${err}`);
    } finally {
      setLoading(false);
    }
  }, [devices]);

  const restartSSH = useCallback(async (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    if (!device) return;

    setLoading(true);
    try {
      await restartSshService(device.serialNumber);

      setDevices(prev => prev.map(d =>
        d.id === deviceId
          ? { ...d, sshStatus: 'reachable', lastSeen: new Date() }
          : d
      ));

      console.log(`Restarted SSH for device ${device.name}`);
    } catch (err) {
      setError(`Failed to restart SSH for device ${device.name}: ${err}`);
    } finally {
      setLoading(false);
    }
  }, [devices]);

  const executeCustomCommand = useCallback(async (deviceId: string, command: string) => {
    const device = devices.find(d => d.id === deviceId);
    if (!device) return;

    setLoading(true);
    try {
      const output = await executeAdbCommand(device.serialNumber, command);

      setDevices(prev => prev.map(d =>
        d.id === deviceId
          ? { ...d, lastSeen: new Date() }
          : d
      ));

      console.log(`Executed command "${command}" on device ${device.name}:`, output);
    } catch (err) {
      setError(`Failed to execute command on device ${device.name}: ${err}`);
    } finally {
      setLoading(false);
    }
  }, [devices]);

  const pingDevice = useCallback(async (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    if (!device) return;

    setLoading(true);
    try {
      const isOnline = await apiPingDevice(device.serialNumber);

      setDevices(prev => prev.map(d =>
        d.id === deviceId
          ? {
              ...d,
              isOnline,
              lastSeen: new Date(),
              adbStatus: isOnline ? 'connected' : 'disconnected',
              sshStatus: isOnline ? 'reachable' : 'unreachable',
            }
          : d
      ));

      console.log(`Pinged device ${device.name}: ${isOnline ? 'success' : 'failed'}`);
    } catch (err) {
      setError(`Failed to ping device ${device.name}: ${err}`);
    } finally {
      setLoading(false);
    }
  }, [devices]);

  // Bulk actions
  const rebootAll = useCallback(async (deviceIds: string[]) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setDevices(prev => prev.map(device => 
        deviceIds.includes(device.id)
          ? { ...device, lastSeen: new Date(), uptime: '0d 0h 0m' }
          : device
      ));
      
      console.log(`Rebooted ${deviceIds.length} devices`);
    } catch (err) {
      setError(`Failed to reboot devices`);
    } finally {
      setLoading(false);
    }
  }, []);

  const pingAll = useCallback(async (deviceIds: string[]) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setDevices(prev => prev.map(device => {
        if (!deviceIds.includes(device.id)) return device;
        
        const success = Math.random() > 0.1;
        return {
          ...device,
          isOnline: success,
          lastSeen: new Date(),
          adbStatus: success ? 'connected' : 'disconnected',
          sshStatus: success ? 'reachable' : 'unreachable',
        };
      }));
      
      console.log(`Pinged ${deviceIds.length} devices`);
    } catch (err) {
      setError(`Failed to ping devices`);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshDevices = useCallback(async () => {
    setLoading(true);
    try {
      const updatedDevices = await refreshDeviceData(devices);
      setDevices(updatedDevices);
      console.log('Refreshed all devices from API');
    } catch (err) {
      console.error('Failed to refresh devices:', err);
      setError('Failed to refresh devices from API');
    } finally {
      setLoading(false);
    }
  }, [devices]);

  // Initialize devices from API
  const initializeDevicesFromApi = useCallback(async () => {
    setLoading(true);
    try {
      const connectedSerials = await getConnectedDevices();
      const apiDevices: Device[] = [];

      for (const serial of connectedSerials) {
        try {
          const apiResponse = await fetchDeviceInfo(serial);
          const device = await transformApiResponseToDevice(apiResponse, {
            name: `Phone-${String(apiDevices.length + 1).padStart(3, '0')}`,
            alias: `${apiResponse.content.build.MANUFACTURER} ${apiResponse.content.build.MODEL}`,
          });
          apiDevices.push(device);
        } catch (err) {
          console.warn(`Failed to fetch device info for ${serial}:`, err);
        }
      }

      setDevices(apiDevices);
      console.log(`Initialized ${apiDevices.length} devices from API`);
    } catch (err) {
      console.error('Failed to initialize devices from API:', err);
      setError('Failed to connect to device API');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch individual device info
  const fetchIndividualDeviceInfo = useCallback(async (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    if (!device) return;

    try {
      const apiResponse = await fetchDeviceInfo(device.serialNumber);
      const updatedDevice = await transformApiResponseToDevice(apiResponse, device);

      setDevices(prev => prev.map(d =>
        d.id === deviceId ? updatedDevice : d
      ));
    } catch (err) {
      console.error(`Failed to fetch info for device ${deviceId}:`, err);
      setError(`Failed to refresh device ${device.name}`);
    }
  }, [devices]);

  // Initialize on mount
  useEffect(() => {
    initializeDevicesFromApi();
  }, [initializeDevicesFromApi]);

  // Clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timeout = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timeout);
    }
  }, [error]);

  return {
    devices,
    stats: getStats(),
    loading,
    error,
    actions: {
      rebootDevice,
      restartSSH,
      executeCustomCommand,
      pingDevice,
      rebootAll,
      pingAll,
      refreshDevices,
      fetchIndividualDeviceInfo,
      initializeDevicesFromApi,
    },
  };
};
