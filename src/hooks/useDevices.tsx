import { useState, useEffect, useCallback } from 'react';
import { refreshDeviceData, getConnectedDevices, transformApiResponseToDevice, fetchDeviceInfo } from '../api/deviceApi';
import type {Device, DeviceStats} from "../types/device.ts";

export const useDevices = () => {
  const [devices, setDevices] = useState<Device[]>(mockDevices);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [useRealApi, setUseRealApi] = useState(true);

  // Calculate device statistics
  const getStats = useCallback((): DeviceStats => {
    const totalDevices = devices.length;
    const onlineDevices = devices.filter(d => d.isOnline).length;
    const offlineDevices = totalDevices - onlineDevices;

    const getDeviceHealth = (device: Device) => {
      if (!device.isOnline) return 'offline';
      
      const issues = [
        device.adbStatus === 'disconnected',
        device.sshStatus === 'unreachable',
        (device.batteryLevel ?? 100) < 20,
      ].filter(Boolean).length;

      if (issues === 0) return 'healthy';
      if (issues === 1) return 'warning';
      return 'critical';
    };

    const healthyDevices = devices.filter(d => getDeviceHealth(d) === 'healthy').length;
    const warningDevices = devices.filter(d => getDeviceHealth(d) === 'warning').length;
    const criticalDevices = devices.filter(d => getDeviceHealth(d) === 'critical').length;

    return {
      totalDevices,
      onlineDevices,
      offlineDevices,
      healthyDevices,
      warningDevices,
      criticalDevices,
    };
  }, [devices]);

  // Simulate device actions
  const rebootDevice = useCallback(async (deviceId: string) => {
    setLoading(true);
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setDevices(prev => prev.map(device => 
        device.id === deviceId 
          ? { ...device, lastSeen: new Date(), uptime: '0d 0h 0m' }
          : device
      ));
      
      console.log(`Rebooted device ${deviceId}`);
    } catch (err) {
      setError(`Failed to reboot device ${deviceId}`);
    } finally {
      setLoading(false);
    }
  }, []);

  const restartSSH = useCallback(async (deviceId: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setDevices(prev => prev.map(device => 
        device.id === deviceId 
          ? { ...device, sshStatus: 'reachable', lastSeen: new Date() }
          : device
      ));
      
      console.log(`Restarted SSH for device ${deviceId}`);
    } catch (err) {
      setError(`Failed to restart SSH for device ${deviceId}`);
    } finally {
      setLoading(false);
    }
  }, []);

  const executeCustomCommand = useCallback(async (deviceId: string, command: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setDevices(prev => prev.map(device => 
        device.id === deviceId 
          ? { ...device, lastSeen: new Date() }
          : device
      ));
      
      console.log(`Executed command "${command}" on device ${deviceId}`);
    } catch (err) {
      setError(`Failed to execute command on device ${deviceId}`);
    } finally {
      setLoading(false);
    }
  }, []);

  const pingDevice = useCallback(async (deviceId: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Simulate ping result - randomly succeed or fail
      const success = Math.random() > 0.1; // 90% success rate
      
      setDevices(prev => prev.map(device => 
        device.id === deviceId 
          ? { 
              ...device, 
              isOnline: success,
              lastSeen: new Date(),
              adbStatus: success ? 'connected' : 'disconnected',
              sshStatus: success ? 'reachable' : 'unreachable',
            }
          : device
      ));
      
      console.log(`Pinged device ${deviceId}: ${success ? 'success' : 'failed'}`);
    } catch (err) {
      setError(`Failed to ping device ${deviceId}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Bulk actions
  const rebootAll = useCallback(async (deviceIds: string[]) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setDevices(prev => prev.map(device => 
        deviceIds.includes(device.id)
          ? { ...device, lastSeen: new Date(), uptime: '0d 0h 0m' }
          : device
      ));
      
      console.log(`Rebooted ${deviceIds.length} devices`);
    } catch (err) {
      setError(`Failed to reboot devices`);
    } finally {
      setLoading(false);
    }
  }, []);

  const pingAll = useCallback(async (deviceIds: string[]) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setDevices(prev => prev.map(device => {
        if (!deviceIds.includes(device.id)) return device;
        
        const success = Math.random() > 0.1;
        return {
          ...device,
          isOnline: success,
          lastSeen: new Date(),
          adbStatus: success ? 'connected' : 'disconnected',
          sshStatus: success ? 'reachable' : 'unreachable',
        };
      }));
      
      console.log(`Pinged ${deviceIds.length} devices`);
    } catch (err) {
      setError(`Failed to ping devices`);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshDevices = useCallback(async () => {
    setLoading(true);
    try {
      if (useRealApi) {
        // Use real API to refresh device data
        const updatedDevices = await refreshDeviceData(devices);
        setDevices(updatedDevices);
        console.log('Refreshed all devices from API');
      } else {
        // Fallback to mock behavior
        await new Promise(resolve => setTimeout(resolve, 1000));
        setDevices(prev => prev.map(device => ({
          ...device,
          lastSeen: new Date(),
          batteryLevel: Math.max(0, Math.min(100, (device.batteryLevel ?? 50) + Math.floor(Math.random() * 10) - 5)),
        })));
        console.log('Refreshed all devices (mock)');
      }
    } catch (err) {
      console.error('Failed to refresh devices:', err);
      setError('Failed to refresh devices from API. Using cached data.');
      setUseRealApi(false); // Fallback to mock mode
    } finally {
      setLoading(false);
    }
  }, [devices, useRealApi]);

  // Initialize devices from API
  const initializeDevicesFromApi = useCallback(async () => {
    setLoading(true);
    try {
      const connectedSerials = await getConnectedDevices();
      const apiDevices: Device[] = [];

      for (const serial of connectedSerials) {
        try {
          const apiResponse = await fetchDeviceInfo(serial);
          const device = transformApiResponseToDevice(apiResponse, {
            ipAddress: `192.168.1.${100 + apiDevices.length}`, // Mock IP for now
            name: `Phone-${String(apiDevices.length + 1).padStart(3, '0')}`,
            alias: `${apiResponse.content.build.MANUFACTURER} ${apiResponse.content.build.MODEL}`,
          });
          apiDevices.push(device);
        } catch (err) {
          console.warn(`Failed to fetch device info for ${serial}:`, err);
        }
      }

      if (apiDevices.length > 0) {
        setDevices(apiDevices);
        console.log(`Initialized ${apiDevices.length} devices from API`);
      } else {
        console.log('No devices found from API, using mock data');
        setUseRealApi(false);
      }
    } catch (err) {
      console.error('Failed to initialize devices from API:', err);
      setError('Failed to connect to device API. Using mock data.');
      setUseRealApi(false);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch individual device info
  const fetchIndividualDeviceInfo = useCallback(async (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    if (!device || !useRealApi) return;

    try {
      const apiResponse = await fetchDeviceInfo(device.serialNumber);
      const updatedDevice = transformApiResponseToDevice(apiResponse, device);

      setDevices(prev => prev.map(d =>
        d.id === deviceId ? updatedDevice : d
      ));
    } catch (err) {
      console.error(`Failed to fetch info for device ${deviceId}:`, err);
    }
  }, [devices, useRealApi]);

  // Initialize on mount
  useEffect(() => {
    if (useRealApi) {
      initializeDevicesFromApi();
    }
  }, [initializeDevicesFromApi, useRealApi]);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      const update = getRandomDeviceUpdate();
      setDevices(prev => prev.map(device => 
        device.id === update.id 
          ? { ...device, ...update }
          : device
      ));
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  // Clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timeout = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timeout);
    }
  }, [error]);

  return {
    devices,
    stats: getStats(),
    loading,
    error,
    useRealApi,
    actions: {
      rebootDevice,
      restartSSH,
      executeCustomCommand,
      pingDevice,
      rebootAll,
      pingAll,
      refreshDevices,
      fetchIndividualDeviceInfo,
      initializeDevicesFromApi,
      toggleApiMode: () => setUseRealApi(!useRealApi),
    },
  };
};
