// API Response interfaces
export interface DeviceBuildInfo {
  MANUFACTURER: string;
  MODEL: string;
  FINGERPRINT: string;
  BRAND: string;
  PRODUCT: string;
  DEVICE: string;
  RELEASE: string;
  ID: string;
  INCREMENTAL: string;
  TYPE: string;
  TAGS: string;
  SECURITY_PATCH: string;
  DEVICE_INITIAL_SDK_INT: string;
}

export interface DeviceProps {
  'build.id': string;
  'security_patch': string;
  'api_level': string;
}

export interface DeviceApiResponse {
  serial: string;
  method: string;
  content: {
    build: DeviceBuildInfo;
    props: DeviceProps;
  };
}

export interface BatteryInfo {
  acPowered: boolean;
  usbPowered: boolean;
  wirelessPowered: boolean;
  maxChargingCurrent: number;
  maxChargingVoltage: number;
  chargeCounter: number;
  status: number; // 1=Unknown, 2=Charging, 3=Discharging, 4=Not charging, 5=Full
  health: number; // 1=Unknown, 2=Good, 3=Overheat, 4=Dead, 5=Over voltage, 6=Unspecified failure, 7=Cold
  present: boolean;
  level: number;
  scale: number;
  voltage: number;
  temperature: number; // in tenths of degrees Celsius
  technology: string;
}

export interface Device {
  id: string;
  name: string;
  alias?: string;
  ipAddress: string;
  serialNumber: string;
  adbStatus: 'connected' | 'disconnected';
  sshStatus: 'reachable' | 'unreachable';
  buildFingerprint: string;
  productModel: string;
  buildVersion: string;
  batteryLevel?: number;
  isCharging?: boolean;
  uptime?: string;
  lastSeen: Date;
  isOnline: boolean;
  // Enhanced API data
  manufacturer?: string;
  brand?: string;
  deviceCodename?: string;
  buildId?: string;
  buildIncremental?: string;
  buildType?: string;
  buildTags?: string;
  securityPatch?: string;
  apiLevel?: string;
  deviceInitialSdk?: string;
  // Detailed battery information
  batteryInfo?: BatteryInfo;
}

export interface DeviceAction {
  id: string;
  type: 'reboot' | 'restart-ssh' | 'custom-command' | 'ping';
  deviceId?: string;
  command?: string;
  timestamp: Date;
  status: 'pending' | 'success' | 'error';
  result?: string;
}

export interface BulkAction {
  type: 'reboot-all' | 'ping-all' | 'export-csv' | 'export-json';
  deviceIds: string[];
  timestamp: Date;
  status: 'pending' | 'success' | 'error';
  results?: Record<string, string>;
}

export type ConnectionStatus = 'connected' | 'disconnected' | 'reachable' | 'unreachable';
export type BatteryStatus = 'low' | 'okay' | 'charging' | 'unknown';
export type DeviceHealth = 'healthy' | 'warning' | 'critical' | 'offline';

export interface DeviceStats {
  totalDevices: number;
  onlineDevices: number;
  offlineDevices: number;
  healthyDevices: number;
  warningDevices: number;
  criticalDevices: number;
}
